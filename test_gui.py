#!/usr/bin/env python3
"""
Test script for PyEM GUI
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

try:
    from pyem_gui import main
    print("Starting PyEM GUI...")
    main()
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure all required packages are installed:")
    print("- tkinter (usually comes with Python)")
    print("- matplotlib")
    print("- numpy")
    print("\nInstall with: pip install matplotlib numpy")
except Exception as e:
    print(f"Error starting GUI: {e}")
    import traceback
    traceback.print_exc()
