#!/usr/bin/env python3
"""
Launcher script for PyEM GUI
Handles dependency checking and provides helpful error messages
"""

import sys
import os
import subprocess

def check_dependencies():
    """Check if required dependencies are available"""
    missing = []
    
    # Check tkinter
    try:
        import tkinter
    except ImportError:
        missing.append("tkinter")
    
    # Check matplotlib
    try:
        import matplotlib
    except ImportError:
        missing.append("matplotlib")
    
    # Check numpy
    try:
        import numpy
    except ImportError:
        missing.append("numpy")
    
    return missing

def install_dependencies(packages):
    """Attempt to install missing packages"""
    print(f"Missing packages: {', '.join(packages)}")
    print("Attempting to install...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install"] + packages)
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """Main launcher function"""
    print("PyEM GUI Launcher")
    print("=" * 50)
    
    # Check dependencies
    missing = check_dependencies()
    
    if missing:
        print(f"Missing required packages: {', '.join(missing)}")
        
        if "tkinter" in missing:
            print("\nNote: tkinter usually comes with Python.")
            print("If missing, you may need to install python3-tk on Linux:")
            print("  sudo apt-get install python3-tk  # Ubuntu/Debian")
            print("  sudo yum install tkinter         # CentOS/RHEL")
            missing.remove("tkinter")
        
        if missing:
            response = input(f"\nTry to install {', '.join(missing)}? (y/n): ")
            if response.lower() == 'y':
                if install_dependencies(missing):
                    print("Dependencies installed successfully!")
                else:
                    print("Failed to install dependencies. Please install manually:")
                    print(f"  pip install {' '.join(missing)}")
                    return 1
            else:
                print("Please install dependencies manually and try again.")
                return 1
    
    # Check if PyEM is available
    pyem_path = os.path.join(os.path.dirname(__file__), 'pyem-master')
    if not os.path.exists(pyem_path):
        print(f"\nWarning: PyEM not found at {pyem_path}")
        print("The GUI will start but PyEM tools may not work.")
        print("Make sure PyEM is properly installed or available in the path.")
    
    # Launch GUI
    print("\nStarting PyEM GUI...")
    try:
        from pyem_gui import main
        main()
    except Exception as e:
        print(f"Error starting GUI: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
