package:
  name: pyem
  version: 0.67

source:
  path: .

build:
  noarch: python
  number: 0
  script: python -m pip install --no-deps --ignore-installed .
  entry_points:
    - cfsc.py = pyem.cli.cfsc:_main_
    - csparc2star.py = pyem.cli.csparc2star:_main_
    - ctf2star.py = pyem.cli.ctf2star:_main_
    - disparticle.py = pyem.cli.disparticle:_main_
    - emcalc.py = pyem.cli.emcalc:_main_
    - map.py = pyem.cli.map:_main_
    - mask.py = pyem.cli.map:_main_
    - mcstar.py = pyem.cli.mcstar:_main_
    - par2star.py = pyem.cli.par2star:_main_
    - project.py = pyem.cli.project:_main_
    - projection_subtraction.py = pyem.cli.projection_subtraction:_main_
    - stack.py = pyem.cli.stack:_main_
    - star.py = pyem.cli.star:_main_
    - star2bild.py = pyem.cli.star2bild:_main_
    - subparticles.py = pyem.cli.subparticles:_main_
    - varmap.py = pyem.cli.varmap:_main_

requirements:
  build:
  host:
    - python
    - pip
    - setuptools
  run:
    - python >=3.9
    - numba >=0.41
    - numpy >=1.14
    - scipy >=1.2
    - matplotlib-base >=2.2
    - seaborn >=0.9
    - pandas >=0.23.4
    - pathos >=0.2.1
    - pyfftw >=0.10
    - healpy >=1.11
    - natsort >=6.0
    - starfile >=0.5.2

about:
  home: https://github.com/asarnow/pyem
  license: GPL-3.0-or-later
  license_family: GPL
  license_file: LICENSE
  summary: Python modules and command-line utilities for electron microscopy of biological samples.
  description: |
    A collection of Python modules and command-line utilities for electron microscopy of biological samples.
    Cite as: Asarnow, D., Palovcak, E., Cheng, Y. UCSF pyem v0.5. Zenodo https://doi.org/10.5281/zenodo.3576630 (2019)
  dev_url: https://github.com/asarnow/pyem

conda:
  channels:
    - conda-forge
    - defaults
