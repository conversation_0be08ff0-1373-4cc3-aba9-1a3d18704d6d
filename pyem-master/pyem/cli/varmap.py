#!/usr/bin/env python
# Copyright (C) 2017 <PERSON>
# University of California, San Francisco
#
# Simple program for computing mean and variance of maps.
# See help text and README file for more information.
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
import numpy as np
import sys
from pyem import mrc


def main(args):
    x = mrc.read(args.input[0])
    m2 = np.zeros(x.shape)
    mu = x.copy()
    for i, f in enumerate(args.input[1:]):
        x = mrc.read(f)
        olddif = x - mu
        mu += (x - mu) / (i + 1)
        m2 += olddif * (x - mu)
    var = m2 / len(args.input)
    mrc.write(args.output, var)
    if args.mean is not None:
        mrc.write(args.mean, mu)
    return 0


def _main_():
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("input", help="Input map path(s)", nargs="*")
    parser.add_argument("output", help="Variance map output path")
    parser.add_argument("--mean", help="Mean map output path")
    sys.exit(main(parser.parse_args()))


if __name__ == "__main__":
    _main_()
