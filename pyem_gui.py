#!/usr/bin/env python3
"""
Comprehensive GUI for PyEM - Electron Microscopy Data Processing
A graphical interface for all PyEM command-line tools
"""

import sys
import os
import json
import logging
import subprocess
import threading
from pathlib import Path
from typing import Dict, List, Any, Optional

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np

# Add pyem to path if needed
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'pyem-master'))

class PyEMGUI:
    """Main GUI application for PyEM tools"""

    def __init__(self, root):
        self.root = root
        self.root.title("PyEM - Comprehensive Electron Microscopy Data Processing")
        self.root.geometry("1400x900")

        # Configure logging
        self.setup_logging()

        # Initialize variables
        self.current_process = None
        self.tool_configs = {}
        self.tool_params = {}
        self.param_widgets = {}

        # Load tool configurations first
        self.load_tool_configs()

        # Create main interface
        self.create_main_interface()

    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

    def create_main_interface(self):
        """Create the main GUI interface"""
        # Create main menu
        self.create_menu()

        # Create main paned window
        main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel - Tool selection and parameters
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=2)

        # Right panel - Output and visualization
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=1)

        # Create tool selection notebook
        self.create_tool_notebook(left_frame)

        # Create output panel
        self.create_output_panel(right_frame)

    def create_menu(self):
        """Create application menu"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Load Configuration", command=self.load_config)
        file_menu.add_command(label="Save Configuration", command=self.save_config)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)

        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Batch Processing", command=self.open_batch_processor)
        tools_menu.add_command(label="File Browser", command=self.open_file_browser)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
        help_menu.add_command(label="Documentation", command=self.show_documentation)

    def create_tool_notebook(self, parent):
        """Create notebook with tabs for different tool categories"""
        self.tool_notebook = ttk.Notebook(parent)
        self.tool_notebook.pack(fill=tk.BOTH, expand=True)

        # Define tool categories
        categories = {
            "Format Conversion": ["csparc2star", "par2star", "ctf2star", "star2bild"],
            "STAR File Operations": ["star"],
            "Stack Operations": ["stack"],
            "Map Processing": ["map", "mask", "varmap", "emcalc"],
            "Projection & Reconstruction": ["project", "projection_subtraction"],
            "Particle Analysis": ["subparticles", "disparticle"],
            "Motion Correction": ["mcstar"],
            "Analysis Tools": ["cfsc"]
        }

        # Create tabs for each category
        for category, tools in categories.items():
            self.create_category_tab(category, tools)

    def create_category_tab(self, category_name: str, tools: List[str]):
        """Create a tab for a specific tool category"""
        # Create frame for this category
        category_frame = ttk.Frame(self.tool_notebook)
        self.tool_notebook.add(category_frame, text=category_name)

        # Create scrollable frame
        canvas = tk.Canvas(category_frame)
        scrollbar = ttk.Scrollbar(category_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Add tools to this category
        for tool in tools:
            self.create_tool_interface(scrollable_frame, tool)

    def create_tool_interface(self, parent, tool_name: str):
        """Create interface for a specific tool"""
        # Create collapsible frame for this tool
        tool_frame = ttk.LabelFrame(parent, text=f"{tool_name}.py")
        tool_frame.pack(fill=tk.X, padx=5, pady=5)

        # Tool description
        description = self.get_tool_description(tool_name)
        desc_label = ttk.Label(tool_frame, text=description, wraplength=400)
        desc_label.pack(anchor=tk.W, padx=5, pady=2)

        # Parameters frame
        params_frame = ttk.Frame(tool_frame)
        params_frame.pack(fill=tk.X, padx=5, pady=5)

        # Create parameter inputs based on tool
        self.create_tool_parameters(params_frame, tool_name)

        # Run button
        run_button = ttk.Button(
            tool_frame,
            text=f"Run {tool_name}",
            command=lambda: self.run_tool(tool_name)
        )
        run_button.pack(pady=5)

    def get_tool_description(self, tool_name: str) -> str:
        """Get description for a tool"""
        descriptions = {
            "csparc2star": "Convert CryoSPARC metadata files to Relion STAR format",
            "par2star": "Convert Frealign PAR files to Relion STAR format",
            "ctf2star": "Convert CTF data to STAR format",
            "star2bild": "Convert STAR files to BILD format for visualization",
            "star": "Manipulate STAR files (merge, filter, transform, etc.)",
            "stack": "Create and manipulate particle image stacks",
            "map": "Process 3D density maps (transform, filter, etc.)",
            "mask": "Create and manipulate 3D masks",
            "varmap": "Compute variance maps from multiple input maps",
            "emcalc": "Calculator for EM data using mathematical expressions",
            "project": "Generate 2D projections from 3D maps",
            "projection_subtraction": "Subtract projections from particle images",
            "subparticles": "Extract subparticles from larger particles",
            "disparticle": "Display particle coordinates on micrographs",
            "mcstar": "Process motion correction STAR files",
            "cfsc": "Compute Fourier Shell Correlation between maps"
        }
        return descriptions.get(tool_name, f"PyEM tool: {tool_name}")

    def create_tool_parameters(self, parent, tool_name: str):
        """Create parameter input widgets for a tool"""
        # Initialize parameter storage for this tool
        if tool_name not in self.tool_params:
            self.tool_params[tool_name] = {}

        # Get parameter definitions for this tool
        param_defs = self.get_tool_parameters(tool_name)

        # Create parameter widgets
        for param_name, param_info in param_defs.items():
            widget = ParameterWidget(
                parent,
                param_name,
                param_info.get('type', 'string'),
                param_info.get('default'),
                param_info.get('help', '')
            )
            widget.create_widget()
            self.param_widgets[f"{tool_name}_{param_name}"] = widget

    def get_tool_parameters(self, tool_name: str) -> Dict[str, Dict[str, Any]]:
        """Get parameter definitions for a specific tool"""

        # Common parameters
        common_params = {
            "input": {"type": "file", "help": "Input file"},
            "output": {"type": "file", "help": "Output file"},
            "loglevel": {"type": "choice", "default": "WARNING", "help": "Logging level"}
        }

        # Tool-specific parameters
        tool_params = {
            "csparc2star": {
                "input": {"type": "file", "help": "CryoSPARC metadata .csv or .cs files"},
                "output": {"type": "file", "help": "Output .star file"},
                "movies": {"type": "bool", "help": "Write per-movie star files"},
                "boxsize": {"type": "float", "help": "CryoSPARC refinement box size"},
                "class": {"type": "string", "help": "Keep this class in output"},
                "minphic": {"type": "float", "default": 0, "help": "Minimum posterior probability"},
                "stack-path": {"type": "file", "help": "Path to single particle stack"},
                "micrograph-path": {"type": "string", "help": "Replacement path for micrographs"},
                "swapxy": {"type": "bool", "help": "Swap X and Y axes"},
                "invertx": {"type": "bool", "help": "Invert particle coordinate X axis"},
                "inverty": {"type": "bool", "help": "Invert particle coordinate Y axis"},
                "relion2": {"type": "bool", "help": "Write Relion2 compatible STAR file"}
            },

            "star": {
                "input": {"type": "file", "help": "Input .star file(s)"},
                "output": {"type": "file", "help": "Output .star file"},
                "class": {"type": "string", "help": "Keep this class in output"},
                "copy-angles": {"type": "file", "help": "Copy Euler angles from this file"},
                "copy-alignments": {"type": "file", "help": "Copy alignment parameters"},
                "merge": {"type": "file", "help": "Merge with this .star file"},
                "subsample": {"type": "float", "help": "Randomly subsample particles"},
                "transform": {"type": "string", "help": "Apply transformation matrix"},
                "relion2": {"type": "bool", "help": "Write Relion2 compatible STAR file"}
            },

            "stack": {
                "input": {"type": "file", "help": "Input image(s), stack(s) and/or .star file(s)"},
                "output": {"type": "file", "help": "Output stack"},
                "star": {"type": "file", "help": "Optional composite .star output file"},
                "stack-path": {"type": "string", "help": "Particle stack for input file"},
                "class": {"type": "string", "help": "Keep this class in output"},
                "relion2": {"type": "bool", "help": "Write Relion2 compatible STAR file"},
                "resort": {"type": "bool", "help": "Natural sort the particle image names"},
                "float16": {"type": "bool", "help": "Output Mode 12 MRC (float16)"}
            },

            "map": {
                "input": {"type": "file", "help": "Input volume (MRC file)"},
                "output": {"type": "file", "help": "Output volume (MRC file)"},
                "apix": {"type": "float", "help": "Pixel size in Angstroms"},
                "mask": {"type": "file", "help": "Final mask"},
                "transpose": {"type": "string", "help": "Swap volume axes order"},
                "flip": {"type": "string", "help": "Flip volume over axis"},
                "euler": {"type": "string", "help": "Euler angles in degrees"},
                "translate": {"type": "string", "help": "Translation coordinates"},
                "boxsize": {"type": "int", "help": "Output box size"},
                "scale": {"type": "float", "help": "Scale factor for output pixel size"}
            },

            "mask": {
                "input": {"type": "file", "help": "Input volume MRC file"},
                "output": {"type": "file", "help": "Output mask MRC file"},
                "threshold": {"type": "float", "help": "Threshold for initial mask"},
                "extend": {"type": "int", "default": 0, "help": "Structuring element size"},
                "edge-width": {"type": "int", "default": 5, "help": "Soft edge width"},
                "minvol": {"type": "int", "help": "Minimum volume for connected components"},
                "fill": {"type": "bool", "help": "Fill holes in initial mask"},
                "relion": {"type": "bool", "help": "Mimics relion_mask_create output"}
            },

            "project": {
                "input": {"type": "file", "help": "STAR file with particle metadata"},
                "output": {"type": "file", "help": "Output particle stack"},
                "map": {"type": "file", "help": "Map used to calculate projections"},
                "mask": {"type": "file", "help": "Mask to apply to map"},
                "ctf": {"type": "bool", "help": "Apply CTF to projections"},
                "flip": {"type": "bool", "help": "Only flip phases when applying CTF"},
                "pfac": {"type": "int", "default": 2, "help": "Zero padding factor"},
                "size": {"type": "int", "help": "Size of projections"}
            },

            "subparticles": {
                "input": {"type": "file", "help": "STAR file with source particles"},
                "output": {"type": "file", "help": "Output file path"},
                "apix": {"type": "float", "help": "Angstroms per pixel"},
                "boxsize": {"type": "int", "help": "Particle box size in pixels"},
                "class": {"type": "string", "help": "Keep this class in output"},
                "displacement": {"type": "float", "default": 0, "help": "Distance along symmetry axis"},
                "sym": {"type": "string", "help": "Symmetry group"},
                "relion2": {"type": "bool", "help": "Write Relion2 compatible STAR file"}
            },

            "varmap": {
                "input": {"type": "file", "help": "Input map path(s)"},
                "output": {"type": "file", "help": "Variance map output path"},
                "mean": {"type": "file", "help": "Mean map output path"}
            },

            "emcalc": {
                "input": {"type": "file", "help": "Input volume (MRC file)"},
                "output": {"type": "file", "help": "Output volume (MRC file)"},
                "apix": {"type": "float", "help": "Output pixel size"},
                "normalize": {"type": "bool", "help": "Normalize all input maps"},
                "eval": {"type": "bool", "help": "Use eval builtin instead of numexpr"}
            },

            "disparticle": {
                "input": {"type": "file", "help": "STAR file with particle coordinates"},
                "output": {"type": "file", "help": "Output image"},
                "micrograph": {"type": "string", "help": "Specific micrograph for display"},
                "fast": {"type": "bool", "help": "Only read first few thousand particles"}
            }
        }

        return tool_params.get(tool_name, common_params)

    def create_output_panel(self, parent):
        """Create output and visualization panel"""
        # Create notebook for output tabs
        output_notebook = ttk.Notebook(parent)
        output_notebook.pack(fill=tk.BOTH, expand=True)

        # Log output tab
        log_frame = ttk.Frame(output_notebook)
        output_notebook.add(log_frame, text="Log Output")

        self.log_text = scrolledtext.ScrolledText(log_frame, height=15)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Progress tab
        progress_frame = ttk.Frame(output_notebook)
        output_notebook.add(progress_frame, text="Progress")

        self.progress_var = tk.StringVar(value="Ready")
        progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        progress_label.pack(pady=10)

        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, padx=20, pady=10)

        # Visualization tab
        viz_frame = ttk.Frame(output_notebook)
        output_notebook.add(viz_frame, text="Visualization")

        # Matplotlib figure for visualization
        self.fig, self.ax = plt.subplots(figsize=(6, 4))
        self.canvas = FigureCanvasTkAgg(self.fig, viz_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def load_tool_configs(self):
        """Load tool configurations and parameter definitions"""
        # Initialize parameter storage for each tool
        self.tool_params = {}

    def log_message(self, message: str, level: str = "INFO"):
        """Add message to log output"""
        timestamp = logging.Formatter().formatTime(logging.LogRecord(
            name="", level=0, pathname="", lineno=0, msg="", args=(), exc_info=None
        ))
        log_entry = f"[{timestamp}] {level}: {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def run_tool(self, tool_name: str):
        """Run a PyEM tool with current parameters"""
        self.log_message(f"Starting {tool_name}...")
        self.progress_var.set(f"Running {tool_name}...")
        self.progress_bar.start()

        # Collect current parameters from widgets
        params = self.collect_tool_parameters(tool_name)

        # Build command
        cmd = self.build_command(tool_name, params)

        # Run in separate thread
        def run_command():
            try:
                self.log_message(f"Executing: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True, cwd="pyem-master")

                if result.returncode == 0:
                    self.log_message(f"{tool_name} completed successfully")
                    if result.stdout:
                        self.log_message(f"Output: {result.stdout}")
                else:
                    self.log_message(f"{tool_name} failed with return code {result.returncode}", "ERROR")
                    if result.stderr:
                        self.log_message(f"Error: {result.stderr}", "ERROR")

            except Exception as e:
                self.log_message(f"Error running {tool_name}: {e}", "ERROR")
            finally:
                self.progress_var.set("Ready")
                self.progress_bar.stop()

        thread = threading.Thread(target=run_command)
        thread.daemon = True
        thread.start()

    def build_command(self, tool_name: str, params: Dict[str, Any]) -> List[str]:
        """Build command line for a tool"""
        cmd = ["python", f"pyem/cli/{tool_name}.py"]

        # Add parameters based on tool
        for param, value in params.items():
            if value and value != "":
                if isinstance(value, bool):
                    if value:
                        cmd.append(f"--{param}")
                else:
                    cmd.extend([f"--{param}", str(value)])

        return cmd

    def load_config(self):
        """Load configuration from file"""
        filename = filedialog.askopenfilename(
            title="Load Configuration",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r') as f:
                    config = json.load(f)
                self.tool_configs.update(config)
                self.log_message(f"Configuration loaded from {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load configuration: {e}")

    def save_config(self):
        """Save current configuration to file"""
        filename = filedialog.asksaveasfilename(
            title="Save Configuration",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w') as f:
                    json.dump(self.tool_configs, f, indent=2)
                self.log_message(f"Configuration saved to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save configuration: {e}")

    def open_batch_processor(self):
        """Open batch processing window"""
        BatchProcessorWindow(self.root, self)

    def open_file_browser(self):
        """Open file browser window"""
        FileBrowserWindow(self.root, self)

    def collect_tool_parameters(self, tool_name: str) -> Dict[str, Any]:
        """Collect current parameter values for a tool"""
        params = {}
        for widget_key, widget in self.param_widgets.items():
            if widget_key.startswith(f"{tool_name}_"):
                param_name = widget_key[len(f"{tool_name}_"):]
                value = widget.get_value()
                if value is not None and value != "":
                    params[param_name] = value
        return params

    def show_about(self):
        """Show about dialog"""
        about_text = """
PyEM GUI v1.0

A comprehensive graphical interface for PyEM -
Python modules and command-line utilities for
electron microscopy of biological samples.

Based on PyEM by Daniel Asarnow, Eugene Palovcak, and Yifan Cheng
University of California, San Francisco

GUI developed with Python/Tkinter
        """
        messagebox.showinfo("About PyEM GUI", about_text)

    def show_documentation(self):
        """Show documentation"""
        messagebox.showinfo("Documentation",
                          "Documentation available at:\nhttps://github.com/asarnow/pyem")


class ParameterWidget:
    """Base class for parameter input widgets"""

    def __init__(self, parent, param_name: str, param_type: str, default_value=None, help_text=""):
        self.parent = parent
        self.param_name = param_name
        self.param_type = param_type
        self.default_value = default_value
        self.help_text = help_text
        self.widget = None
        self.var = None

    def create_widget(self):
        """Create the appropriate widget based on parameter type"""
        frame = ttk.Frame(self.parent)
        frame.pack(fill=tk.X, padx=5, pady=2)

        # Label
        label = ttk.Label(frame, text=f"{self.param_name}:")
        label.pack(side=tk.LEFT)

        # Widget based on type
        if self.param_type == "file":
            self.create_file_widget(frame)
        elif self.param_type == "directory":
            self.create_directory_widget(frame)
        elif self.param_type == "bool":
            self.create_bool_widget(frame)
        elif self.param_type == "int":
            self.create_int_widget(frame)
        elif self.param_type == "float":
            self.create_float_widget(frame)
        elif self.param_type == "choice":
            self.create_choice_widget(frame)
        else:
            self.create_string_widget(frame)

        # Help button
        if self.help_text:
            help_btn = ttk.Button(frame, text="?", width=3,
                                command=lambda: messagebox.showinfo("Help", self.help_text))
            help_btn.pack(side=tk.RIGHT)

    def create_file_widget(self, parent):
        """Create file selection widget"""
        self.var = tk.StringVar(value=self.default_value or "")
        entry = ttk.Entry(parent, textvariable=self.var, width=40)
        entry.pack(side=tk.LEFT, padx=5)

        browse_btn = ttk.Button(parent, text="Browse",
                              command=lambda: self.browse_file())
        browse_btn.pack(side=tk.LEFT)

    def browse_file(self):
        """Open file browser"""
        filename = filedialog.askopenfilename()
        if filename:
            self.var.set(filename)

    def create_directory_widget(self, parent):
        """Create directory selection widget"""
        self.var = tk.StringVar(value=self.default_value or "")
        entry = ttk.Entry(parent, textvariable=self.var, width=40)
        entry.pack(side=tk.LEFT, padx=5)

        browse_btn = ttk.Button(parent, text="Browse",
                              command=lambda: self.browse_directory())
        browse_btn.pack(side=tk.LEFT)

    def browse_directory(self):
        """Open directory browser"""
        dirname = filedialog.askdirectory()
        if dirname:
            self.var.set(dirname)

    def create_bool_widget(self, parent):
        """Create boolean checkbox widget"""
        self.var = tk.BooleanVar(value=self.default_value or False)
        checkbox = ttk.Checkbutton(parent, variable=self.var)
        checkbox.pack(side=tk.LEFT, padx=5)

    def create_int_widget(self, parent):
        """Create integer input widget"""
        self.var = tk.IntVar(value=self.default_value or 0)
        spinbox = ttk.Spinbox(parent, from_=0, to=999999, textvariable=self.var, width=10)
        spinbox.pack(side=tk.LEFT, padx=5)

    def create_float_widget(self, parent):
        """Create float input widget"""
        self.var = tk.DoubleVar(value=self.default_value or 0.0)
        entry = ttk.Entry(parent, textvariable=self.var, width=10)
        entry.pack(side=tk.LEFT, padx=5)

    def create_choice_widget(self, parent):
        """Create choice/combobox widget"""
        self.var = tk.StringVar(value=self.default_value or "")
        combobox = ttk.Combobox(parent, textvariable=self.var, width=15)
        combobox.pack(side=tk.LEFT, padx=5)

    def create_string_widget(self, parent):
        """Create string input widget"""
        self.var = tk.StringVar(value=self.default_value or "")
        entry = ttk.Entry(parent, textvariable=self.var, width=30)
        entry.pack(side=tk.LEFT, padx=5)

    def get_value(self):
        """Get current value from widget"""
        if self.var:
            return self.var.get()
        return None


class BatchProcessorWindow:
    """Window for batch processing multiple files"""

    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app

        # Create window
        self.window = tk.Toplevel(parent)
        self.window.title("Batch Processor")
        self.window.geometry("800x600")

        # Create interface
        self.create_interface()

    def create_interface(self):
        """Create batch processor interface"""
        # File list frame
        list_frame = ttk.LabelFrame(self.window, text="Files to Process")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # File listbox with scrollbar
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.file_listbox = tk.Listbox(list_container)
        scrollbar = ttk.Scrollbar(list_container, orient=tk.VERTICAL, command=self.file_listbox.yview)
        self.file_listbox.configure(yscrollcommand=scrollbar.set)

        self.file_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Buttons frame
        btn_frame = ttk.Frame(list_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(btn_frame, text="Add Files", command=self.add_files).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="Remove Selected", command=self.remove_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="Clear All", command=self.clear_all).pack(side=tk.LEFT, padx=5)

        # Tool selection
        tool_frame = ttk.LabelFrame(self.window, text="Tool Selection")
        tool_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(tool_frame, text="Tool:").pack(side=tk.LEFT, padx=5)
        self.tool_var = tk.StringVar()
        tool_combo = ttk.Combobox(tool_frame, textvariable=self.tool_var,
                                 values=["csparc2star", "star", "stack", "map", "mask"])
        tool_combo.pack(side=tk.LEFT, padx=5)

        # Control buttons
        control_frame = ttk.Frame(self.window)
        control_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(control_frame, text="Start Batch", command=self.start_batch).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Close", command=self.window.destroy).pack(side=tk.RIGHT, padx=5)

    def add_files(self):
        """Add files to batch list"""
        files = filedialog.askopenfilenames(title="Select Files to Process")
        for file in files:
            self.file_listbox.insert(tk.END, file)

    def remove_selected(self):
        """Remove selected files from list"""
        selection = self.file_listbox.curselection()
        for index in reversed(selection):
            self.file_listbox.delete(index)

    def clear_all(self):
        """Clear all files from list"""
        self.file_listbox.delete(0, tk.END)

    def start_batch(self):
        """Start batch processing"""
        files = list(self.file_listbox.get(0, tk.END))
        tool = self.tool_var.get()

        if not files:
            messagebox.showwarning("Warning", "No files selected for processing")
            return

        if not tool:
            messagebox.showwarning("Warning", "No tool selected")
            return

        # Process files (simplified implementation)
        self.main_app.log_message(f"Starting batch processing of {len(files)} files with {tool}")
        messagebox.showinfo("Batch Processing", f"Processing {len(files)} files with {tool}")


class FileBrowserWindow:
    """Window for browsing and managing files"""

    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app

        # Create window
        self.window = tk.Toplevel(parent)
        self.window.title("File Browser")
        self.window.geometry("900x700")

        # Current directory
        self.current_dir = os.getcwd()

        # Create interface
        self.create_interface()
        self.refresh_files()

    def create_interface(self):
        """Create file browser interface"""
        # Navigation frame
        nav_frame = ttk.Frame(self.window)
        nav_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(nav_frame, text="Up", command=self.go_up).pack(side=tk.LEFT, padx=5)
        ttk.Button(nav_frame, text="Home", command=self.go_home).pack(side=tk.LEFT, padx=5)
        ttk.Button(nav_frame, text="Refresh", command=self.refresh_files).pack(side=tk.LEFT, padx=5)

        # Path display
        self.path_var = tk.StringVar(value=self.current_dir)
        path_entry = ttk.Entry(nav_frame, textvariable=self.path_var, width=50)
        path_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # File tree
        tree_frame = ttk.Frame(self.window)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create treeview with scrollbars
        self.tree = ttk.Treeview(tree_frame, columns=("size", "modified"), show="tree headings")
        self.tree.heading("#0", text="Name")
        self.tree.heading("size", text="Size")
        self.tree.heading("modified", text="Modified")

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack tree and scrollbars
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")

        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        # Bind double-click
        self.tree.bind("<Double-1>", self.on_double_click)

        # Info frame
        info_frame = ttk.LabelFrame(self.window, text="File Information")
        info_frame.pack(fill=tk.X, padx=5, pady=5)

        self.info_text = tk.Text(info_frame, height=4, wrap=tk.WORD)
        self.info_text.pack(fill=tk.X, padx=5, pady=5)

    def refresh_files(self):
        """Refresh file listing"""
        # Clear tree
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Update path display
        self.path_var.set(self.current_dir)

        try:
            # List directory contents
            items = os.listdir(self.current_dir)
            items.sort()

            for item in items:
                item_path = os.path.join(self.current_dir, item)

                if os.path.isdir(item_path):
                    # Directory
                    self.tree.insert("", tk.END, text=f"📁 {item}", values=("", ""))
                else:
                    # File
                    try:
                        stat = os.stat(item_path)
                        size = f"{stat.st_size:,} bytes"
                        modified = os.path.getmtime(item_path)
                        mod_time = os.path.strftime("%Y-%m-%d %H:%M", os.path.localtime(modified))
                        self.tree.insert("", tk.END, text=f"📄 {item}", values=(size, mod_time))
                    except:
                        self.tree.insert("", tk.END, text=f"📄 {item}", values=("", ""))

        except PermissionError:
            messagebox.showerror("Error", "Permission denied accessing directory")
        except Exception as e:
            messagebox.showerror("Error", f"Error reading directory: {e}")

    def go_up(self):
        """Go to parent directory"""
        parent = os.path.dirname(self.current_dir)
        if parent != self.current_dir:  # Not at root
            self.current_dir = parent
            self.refresh_files()

    def go_home(self):
        """Go to home directory"""
        self.current_dir = os.path.expanduser("~")
        self.refresh_files()

    def on_double_click(self, event):
        """Handle double-click on tree item"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            name = item["text"]

            # Remove emoji prefix
            if name.startswith("📁 "):
                name = name[2:]
                # Directory - navigate into it
                new_path = os.path.join(self.current_dir, name)
                if os.path.isdir(new_path):
                    self.current_dir = new_path
                    self.refresh_files()
            elif name.startswith("📄 "):
                name = name[2:]
                # File - show info
                file_path = os.path.join(self.current_dir, name)
                self.show_file_info(file_path)

    def show_file_info(self, file_path):
        """Show file information"""
        try:
            stat = os.stat(file_path)
            info = f"File: {os.path.basename(file_path)}\n"
            info += f"Path: {file_path}\n"
            info += f"Size: {stat.st_size:,} bytes\n"
            info += f"Modified: {os.path.getmtime(file_path)}"

            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, info)
        except Exception as e:
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, f"Error getting file info: {e}")


def main():
    """Main entry point"""
    root = tk.Tk()
    app = PyEMGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
