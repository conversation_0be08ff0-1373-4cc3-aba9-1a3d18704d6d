# PyEM Comprehensive GUI

A comprehensive graphical user interface for PyEM - Python modules and command-line utilities for electron microscopy of biological samples.

## Features

### Main Interface
- **Tabbed Interface**: Organized by tool categories (Format Conversion, STAR File Operations, Stack Operations, Map Processing, etc.)
- **Parameter Input**: Intuitive widgets for all tool parameters with help text
- **Real-time Logging**: Monitor tool execution with detailed log output
- **Progress Tracking**: Visual progress indicators for running tools
- **Configuration Management**: Save and load parameter configurations

### Tool Categories

1. **Format Conversion**
   - `csparc2star`: Convert CryoSPARC metadata to Relion STAR format
   - `par2star`: Convert Frealign PAR files to Relion STAR format
   - `ctf2star`: Convert CTF data to STAR format
   - `star2bild`: Convert STAR files to BILD format

2. **STAR File Operations**
   - `star`: Manipulate STAR files (merge, filter, transform, etc.)

3. **Stack Operations**
   - `stack`: Create and manipulate particle image stacks

4. **Map Processing**
   - `map`: Process 3D density maps
   - `mask`: Create and manipulate 3D masks
   - `varmap`: Compute variance maps
   - `emcalc`: Calculator for EM data

5. **Projection & Reconstruction**
   - `project`: Generate 2D projections from 3D maps
   - `projection_subtraction`: Subtract projections

6. **Particle Analysis**
   - `subparticles`: Extract subparticles from larger particles
   - `disparticle`: Display particle coordinates on micrographs

7. **Motion Correction**
   - `mcstar`: Process motion correction STAR files

8. **Analysis Tools**
   - `cfsc`: Compute Fourier Shell Correlation

### Additional Features

- **Batch Processor**: Process multiple files with the same tool and parameters
- **File Browser**: Navigate and manage files with a built-in file browser
- **Visualization**: Basic plotting capabilities for results
- **Help System**: Integrated help for all parameters

## Installation

### Prerequisites
- Python 3.9 or higher
- PyEM installed (see main PyEM documentation)

### Install GUI Dependencies
```bash
pip install -r requirements_gui.txt
```

### Required Packages
- `matplotlib` - For plotting and visualization
- `numpy` - For numerical operations
- `tkinter` - GUI framework (usually included with Python)

## Usage

### Starting the GUI
```bash
python pyem_gui.py
```

Or use the test script:
```bash
python test_gui.py
```

### Basic Workflow

1. **Select Tool Category**: Click on the appropriate tab (e.g., "Format Conversion")
2. **Choose Tool**: Find the tool you want to use (e.g., "csparc2star")
3. **Set Parameters**: 
   - Fill in required parameters (input/output files)
   - Adjust optional parameters as needed
   - Use the "?" buttons for parameter help
4. **Run Tool**: Click the "Run [tool]" button
5. **Monitor Progress**: Watch the log output and progress indicator
6. **View Results**: Check the output files and any visualization

### File Selection
- Use "Browse" buttons to select input/output files
- File paths can be absolute or relative to the working directory
- The file browser tool can help navigate directories

### Configuration Management
- **Save Configuration**: File → Save Configuration (saves all current parameters)
- **Load Configuration**: File → Load Configuration (restores saved parameters)

### Batch Processing
1. Tools → Batch Processing
2. Add multiple input files
3. Select the tool to apply
4. Start batch processing

## Tool-Specific Notes

### csparc2star
- Most popular PyEM tool for CryoSPARC to Relion conversion
- Requires CryoSPARC .cs or .csv files as input
- May need passthrough files for some CryoSPARC job types

### star
- Swiss-army knife for STAR file manipulation
- Can merge, filter, transform, and modify STAR files
- Supports both Relion 2 and 3+ formats

### map
- Comprehensive map processing tool
- Supports transformations, filtering, and format conversion
- Can handle MRC files and various operations

### mask
- Create masks from density maps
- Supports various morphological operations
- Can create soft-edge masks

## Troubleshooting

### Common Issues

1. **Import Errors**
   - Make sure PyEM is properly installed
   - Check that all GUI dependencies are installed
   - Verify Python path includes PyEM modules

2. **Tool Execution Errors**
   - Check that input files exist and are readable
   - Verify output directory is writable
   - Review parameter values for correctness

3. **GUI Display Issues**
   - Ensure tkinter is available (usually included with Python)
   - Try updating matplotlib if visualization issues occur

### Getting Help

- Use the "?" buttons next to parameters for help text
- Check the log output for detailed error messages
- Refer to the main PyEM documentation for tool-specific details
- Visit the PyEM GitHub repository: https://github.com/asarnow/pyem

## Development

The GUI is built with:
- **tkinter**: Main GUI framework
- **matplotlib**: Plotting and visualization
- **threading**: Background tool execution
- **subprocess**: Running PyEM command-line tools

### Extending the GUI

To add support for new PyEM tools:
1. Add tool to appropriate category in `create_tool_notebook()`
2. Add parameter definitions in `get_tool_parameters()`
3. Add tool description in `get_tool_description()`

## License

This GUI is provided under the same license as PyEM (GPL-3.0-or-later).

## Acknowledgments

Based on PyEM by Daniel Asarnow, Eugene Palovcak, and Yifan Cheng
University of California, San Francisco

GUI developed to make PyEM tools more accessible to the electron microscopy community.
